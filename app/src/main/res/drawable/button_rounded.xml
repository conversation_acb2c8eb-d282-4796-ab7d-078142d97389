<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#5A5A5A" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#2196F3" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
