<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.SetApn" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_orange</item>
        <item name="colorSecondaryVariant">@color/accent_orange_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/surface_white</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>

    <!-- Custom button styles -->
    <style name="PrimaryButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:elevation">4dp</item>
    </style>

    <style name="DangerButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/error_red</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:elevation">4dp</item>
    </style>

    <style name="CardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">8dp</item>
        <item name="android:layout_margin">16dp</item>
        <item name="cardBackgroundColor">@color/surface_white</item>
    </style>
</resources>